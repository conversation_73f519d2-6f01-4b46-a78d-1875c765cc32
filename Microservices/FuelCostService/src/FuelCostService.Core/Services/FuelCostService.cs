using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Models;

namespace FuelCostService.Core.Services
{
    /// <summary>
    /// Service implementation for FuelCosts business logic
    /// </summary>
    public class FuelCostService : IFuelCostService
    {
        private readonly IFuelCostRepository _repository;

        public FuelCostService(IFuelCostRepository repository)
        {
            _repository = repository;
        }

        public async Task<IEnumerable<FuelCosts>> GetAllFuelCostsAsync()
        {
            var fuelCostsList = await _repository.GetAllAsync();
            
            // Populate fuel types for each FuelCosts
            foreach (var fuelCosts in fuelCostsList)
            {
                await PopulateFuelTypesAsync(fuelCosts);
            }
            
            return fuelCostsList;
        }

        public async Task<FuelCosts?> GetFuelCostsByHouseIdAsync(Guid houseId)
        {
            var fuelCosts = await _repository.GetByHouseIdAsync(houseId);
            
            if (fuelCosts != null)
            {
                await PopulateFuelTypesAsync(fuelCosts);
            }
            
            return fuelCosts;
        }

        public async Task<FuelCosts?> GetFuelCostsByIdAsync(Guid id)
        {
            var fuelCosts = await _repository.GetByIdAsync(id);
            
            if (fuelCosts != null)
            {
                await PopulateFuelTypesAsync(fuelCosts);
            }
            
            return fuelCosts;
        }

        public async Task<FuelCosts> CreateFuelCostsAsync(FuelCosts fuelCosts)
        {
            if (fuelCosts.Id == Guid.Empty)
                fuelCosts.Id = Guid.NewGuid();

            var createdFuelCosts = await _repository.CreateAsync(fuelCosts);
            
            // Create the associated FuelCostByType entities if they exist
            await CreateFuelCostByTypesAsync(createdFuelCosts);
            
            return createdFuelCosts;
        }

        public async Task UpdateFuelCostsAsync(FuelCosts fuelCosts)
        {
            // Check if the entity exists first
            var existingEntity = await _repository.GetByIdAsync(fuelCosts.Id);
            if (existingEntity == null)
            {
                throw new InvalidOperationException($"FuelCosts with ID {fuelCosts.Id} not found");
            }

            await _repository.UpdateAsync(fuelCosts);
            
            // Update the associated FuelCostByType entities if they exist
            await UpdateFuelCostByTypesAsync(fuelCosts);
        }

        public async Task DeleteFuelCostsAsync(Guid id)
        {
            await _repository.DeleteAsync(id);
        }



        // Private helper methods

        private async Task PopulateFuelTypesAsync(FuelCosts fuelCosts)
        {
            var fuelTypes = await _repository.GetFuelCostByTypesByFuelCostsIdAsync(fuelCosts.Id);

            // Clear existing lists
            fuelCosts.Electricity.Clear();
            fuelCosts.NaturalGas.Clear();
            fuelCosts.Oil.Clear();
            fuelCosts.Propane.Clear();
            fuelCosts.Wood.Clear();

            // Map fuel types based on InternalId
            // Assuming standard InternalId mappings:
            // 1 = Electricity, 2 = Natural Gas, 3 = Oil, 4 = Propane, 5 = Wood
            fuelCosts.Electricity.AddRange(fuelTypes.Where(f => f.InternalId == 1));
            fuelCosts.NaturalGas.AddRange(fuelTypes.Where(f => f.InternalId == 2));
            fuelCosts.Oil.AddRange(fuelTypes.Where(f => f.InternalId == 3));
            fuelCosts.Propane.AddRange(fuelTypes.Where(f => f.InternalId == 4));
            fuelCosts.Wood.AddRange(fuelTypes.Where(f => f.InternalId == 5));
        }

        private async Task CreateFuelCostByTypesAsync(FuelCosts fuelCosts)
        {
            var fuelTypesToCreate = new List<FuelCostByType>();

            if (fuelCosts.Electricity != null && fuelCosts.Electricity.Any())
            {
                foreach (var electricity in fuelCosts.Electricity)
                {
                    // Ensure new ID to avoid tracking conflicts
                    electricity.Id = Guid.NewGuid();
                    electricity.FuelCostsId = fuelCosts.Id;
                    electricity.InternalId = 1;

                    // Ensure child entities also have new IDs
                    EnsureNewIdsForChildEntities(electricity);

                    fuelTypesToCreate.Add(electricity);
                }
            }

            if (fuelCosts.NaturalGas != null && fuelCosts.NaturalGas.Any())
            {
                foreach (var naturalGas in fuelCosts.NaturalGas)
                {
                    // Ensure new ID to avoid tracking conflicts
                    naturalGas.Id = Guid.NewGuid();
                    naturalGas.FuelCostsId = fuelCosts.Id;
                    naturalGas.InternalId = 2;

                    // Ensure child entities also have new IDs
                    EnsureNewIdsForChildEntities(naturalGas);

                    fuelTypesToCreate.Add(naturalGas);
                }
            }

            if (fuelCosts.Oil != null && fuelCosts.Oil.Any())
            {
                foreach (var oil in fuelCosts.Oil)
                {
                    // Ensure new ID to avoid tracking conflicts
                    oil.Id = Guid.NewGuid();
                    oil.FuelCostsId = fuelCosts.Id;
                    oil.InternalId = 3;

                    // Ensure child entities also have new IDs
                    EnsureNewIdsForChildEntities(oil);

                    fuelTypesToCreate.Add(oil);
                }
            }

            if (fuelCosts.Propane != null && fuelCosts.Propane.Any())
            {
                foreach (var propane in fuelCosts.Propane)
                {
                    // Ensure new ID to avoid tracking conflicts
                    propane.Id = Guid.NewGuid();
                    propane.FuelCostsId = fuelCosts.Id;
                    propane.InternalId = 4;

                    // Ensure child entities also have new IDs
                    EnsureNewIdsForChildEntities(propane);

                    fuelTypesToCreate.Add(propane);
                }
            }

            if (fuelCosts.Wood != null && fuelCosts.Wood.Any())
            {
                foreach (var wood in fuelCosts.Wood)
                {
                    // Ensure new ID to avoid tracking conflicts
                    wood.Id = Guid.NewGuid();
                    wood.FuelCostsId = fuelCosts.Id;
                    wood.InternalId = 5;

                    // Ensure child entities also have new IDs
                    EnsureNewIdsForChildEntities(wood);

                    fuelTypesToCreate.Add(wood);
                }
            }

            foreach (var fuelType in fuelTypesToCreate)
            {
                await _repository.CreateFuelCostByTypeAsync(fuelType);
            }
        }

        private void EnsureNewIdsForChildEntities(FuelCostByType fuelCostByType)
        {
            // Ensure Minimum has a new ID
            if (fuelCostByType.Minimum != null)
            {
                fuelCostByType.Minimum.Id = Guid.NewGuid();
                fuelCostByType.Minimum.FuelCostByTypeId = fuelCostByType.Id;
            }

            // Ensure RateBlocks has a new ID
            if (fuelCostByType.RateBlocks != null)
            {
                fuelCostByType.RateBlocks.Id = Guid.NewGuid();
                fuelCostByType.RateBlocks.FuelCostByTypeId = fuelCostByType.Id;

                // Ensure individual rate blocks have new IDs
                if (fuelCostByType.RateBlocks.Block1 != null)
                {
                    fuelCostByType.RateBlocks.Block1.Id = Guid.NewGuid();
                    fuelCostByType.RateBlocks.Block1.FuelCostByTypeId = fuelCostByType.Id;
                }
                if (fuelCostByType.RateBlocks.Block2 != null)
                {
                    fuelCostByType.RateBlocks.Block2.Id = Guid.NewGuid();
                    fuelCostByType.RateBlocks.Block2.FuelCostByTypeId = fuelCostByType.Id;
                }
                if (fuelCostByType.RateBlocks.Block3 != null)
                {
                    fuelCostByType.RateBlocks.Block3.Id = Guid.NewGuid();
                    fuelCostByType.RateBlocks.Block3.FuelCostByTypeId = fuelCostByType.Id;
                }
                if (fuelCostByType.RateBlocks.Block4 != null)
                {
                    fuelCostByType.RateBlocks.Block4.Id = Guid.NewGuid();
                    fuelCostByType.RateBlocks.Block4.FuelCostByTypeId = fuelCostByType.Id;
                }
            }
        }

        private async Task UpdateFuelCostByTypesAsync(FuelCosts fuelCosts)
        {
            var fuelTypesToUpdate = new List<FuelCostByType>();

            if (fuelCosts.Electricity != null && fuelCosts.Electricity.Any())
            {
                foreach (var electricity in fuelCosts.Electricity)
                {
                    electricity.FuelCostsId = fuelCosts.Id;
                    electricity.InternalId = 1;
                    fuelTypesToUpdate.Add(electricity);
                }
            }

            if (fuelCosts.NaturalGas != null && fuelCosts.NaturalGas.Any())
            {
                foreach (var naturalGas in fuelCosts.NaturalGas)
                {
                    naturalGas.FuelCostsId = fuelCosts.Id;
                    naturalGas.InternalId = 2;
                    fuelTypesToUpdate.Add(naturalGas);
                }
            }

            if (fuelCosts.Oil != null && fuelCosts.Oil.Any())
            {
                foreach (var oil in fuelCosts.Oil)
                {
                    oil.FuelCostsId = fuelCosts.Id;
                    oil.InternalId = 3;
                    fuelTypesToUpdate.Add(oil);
                }
            }

            if (fuelCosts.Propane != null && fuelCosts.Propane.Any())
            {
                foreach (var propane in fuelCosts.Propane)
                {
                    propane.FuelCostsId = fuelCosts.Id;
                    propane.InternalId = 4;
                    fuelTypesToUpdate.Add(propane);
                }
            }

            if (fuelCosts.Wood != null && fuelCosts.Wood.Any())
            {
                foreach (var wood in fuelCosts.Wood)
                {
                    wood.FuelCostsId = fuelCosts.Id;
                    wood.InternalId = 5;
                    fuelTypesToUpdate.Add(wood);
                }
            }

            foreach (var fuelType in fuelTypesToUpdate)
            {
                await _repository.UpdateFuelCostByTypeAsync(fuelType);
            }
        }
    }
}