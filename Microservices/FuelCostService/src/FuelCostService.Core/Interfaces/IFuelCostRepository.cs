using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FuelCostService.Core.Models;

namespace FuelCostService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for FuelCosts operations
    /// </summary>
    public interface IFuelCostRepository
    {
        /// <summary>
        /// Gets all fuel costs
        /// </summary>
        Task<IEnumerable<FuelCosts>> GetAllAsync();

        /// <summary>
        /// Gets fuel costs by house ID
        /// </summary>
        Task<FuelCosts?> GetByHouseIdAsync(Guid houseId);

        /// <summary>
        /// Gets fuel costs by ID
        /// </summary>
        Task<FuelCosts?> GetByIdAsync(Guid id);

        /// <summary>
        /// Creates new fuel costs
        /// </summary>
        Task<FuelCosts> CreateAsync(FuelCosts fuelCosts);

        /// <summary>
        /// Updates existing fuel costs
        /// </summary>
        Task UpdateAsync(FuelCosts fuelCosts);

        /// <summary>
        /// Deletes fuel costs by ID
        /// </summary>
        Task DeleteAsync(Guid id);

        /// <summary>
        /// Gets fuel costs by energy upgrade ID
        /// </summary>
        Task<FuelCosts?> GetByEnergyUpgradeIdAsync(Guid energyUpgradeId);
    }
}
