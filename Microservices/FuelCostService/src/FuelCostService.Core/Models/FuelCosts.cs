using System;
using System.Collections.Generic;

namespace FuelCostService.Core.Models
{
    public class FuelCosts
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public bool IncludeCostCalculations { get; set; }
        public string LibraryFile { get; set; } = string.Empty;

        public List<FuelCostByType> Electricity { get; set; } = new List<FuelCostByType>();
        public List<FuelCostByType> NaturalGas { get; set; } = new List<FuelCostByType>();
        public List<FuelCostByType> Oil { get; set; } = new List<FuelCostByType>();
        public List<FuelCostByType> Propane { get; set; } = new List<FuelCostByType>();
        public List<FuelCostByType> Wood { get; set; } = new List<FuelCostByType>();

        public FuelCostsMonthly Monthly { get; set; } = new FuelCostsMonthly();

        public FuelCosts()
        {
        }

        public FuelCosts(FuelCosts toCopy)
        {
            if (toCopy != null)
            {
                Id = Guid.NewGuid();
                HouseId = toCopy.HouseId;
                EnergyUpgradeId = toCopy.EnergyUpgradeId;
                IncludeCostCalculations = toCopy.IncludeCostCalculations;
                LibraryFile = toCopy.LibraryFile;
                
                // Copy fuel cost lists
                foreach (var fuel in toCopy.Electricity)
                    Electricity.Add(new FuelCostByType(fuel));
                    
                foreach (var fuel in toCopy.NaturalGas)
                    NaturalGas.Add(new FuelCostByType(fuel));
                    
                foreach (var fuel in toCopy.Oil)
                    Oil.Add(new FuelCostByType(fuel));
                    
                foreach (var fuel in toCopy.Propane)
                    Propane.Add(new FuelCostByType(fuel));
                    
                foreach (var fuel in toCopy.Wood)
                    Wood.Add(new FuelCostByType(fuel));
                
                Monthly = new FuelCostsMonthly(toCopy.Monthly);
            }
        }

        public FuelCosts CreateCopyForEnergyUpgrade(Guid energyUpgradeId)
        {
            var copy = new FuelCosts(this);
            copy.EnergyUpgradeId = energyUpgradeId;
            return copy;
        }
    }
}
