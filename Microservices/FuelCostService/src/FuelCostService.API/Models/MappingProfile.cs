using System;
using AutoMapper;
using FuelCostService.Core.Models;
using FuelCostService.API.Models;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// AutoMapper profile for mapping between Core Models and DTOs
    /// </summary>
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Main FuelCosts mappings with custom logic for fuel type collections
            CreateMap<FuelCosts, FuelCostsDto>().ReverseMap()
                .AfterMap((src, dest, context) =>
                {
                    // When mapping from DTO to domain model, set the correct InternalId based on collection
                    SetInternalIdForFuelTypes(src.Electricity, 1);
                    SetInternalIdForFuelTypes(src.NaturalGas, 2);
                    SetInternalIdForFuelTypes(src.Oil, 3);
                    SetInternalIdForFuelTypes(src.Propane, 4);
                    SetInternalIdForFuelTypes(src.Wood, 5);
                });

            // FuelCostByType mappings
            CreateMap<FuelCostByType, FuelCostByTypeDto>().ReverseMap();

            // CodeAndText mappings
            CreateMap<CodeAndText, CodeAndTextDto>().ReverseMap();

            // FuelCostMinimum mappings
            CreateMap<FuelCostMinimum, FuelCostMinimumDto>().ReverseMap();

            // FuelCostRateBlocks mappings
            CreateMap<FuelCostRateBlocks, FuelCostRateBlocksDto>().ReverseMap();

            // FuelCostRateBlock mappings
            CreateMap<FuelCostRateBlock, FuelCostRateBlockDto>().ReverseMap();

            // FuelCostsMonthly mappings
            CreateMap<FuelCostsMonthly, FuelCostsMonthlyDto>().ReverseMap();

            // FuelCostMonthlyData mappings
            CreateMap<FuelCostMonthlyData, FuelCostMonthlyDataDto>().ReverseMap();
        }
    }
}