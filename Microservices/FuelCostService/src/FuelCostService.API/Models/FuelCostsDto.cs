using System;
using System.Collections.Generic;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for main FuelCosts aggregate (matches FuelCosts.cs model)
    /// </summary>
    public class FuelCostsDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public bool IncludeCostCalculations { get; set; }
        public string LibraryFile { get; set; } = string.Empty;
        
        public List<FuelCostByTypeDto> Electricity { get; set; } = new List<FuelCostByTypeDto>();
        public List<FuelCostByTypeDto> NaturalGas { get; set; } = new List<FuelCostByTypeDto>();
        public List<FuelCostByTypeDto> Oil { get; set; } = new List<FuelCostByTypeDto>();
        public List<FuelCostByTypeDto> Propane { get; set; } = new List<FuelCostByTypeDto>();
        public List<FuelCostByTypeDto> Wood { get; set; } = new List<FuelCostByTypeDto>();
        
        public FuelCostsMonthlyDto Monthly { get; set; } = new FuelCostsMonthlyDto();
    }
}
