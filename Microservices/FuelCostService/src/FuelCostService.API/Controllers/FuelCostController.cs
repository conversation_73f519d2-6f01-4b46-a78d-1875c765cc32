using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using FuelCostService.API.Models;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace FuelCostService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FuelCostController : ControllerBase
    {
        private readonly IFuelCostService _fuelCostService;
        private readonly IMapper _mapper;
        private readonly ILogger<FuelCostController> _logger;

        public FuelCostController(
            IFuelCostService fuelCostService,
            IMapper mapper,
            ILogger<FuelCostController> logger)
        {
            _fuelCostService = fuelCostService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Gets all fuel costs
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<FuelCostsDto>>> GetAllFuelCosts()
        {
            _logger.LogInformation("Getting all fuel costs");

            var fuelCosts = await _fuelCostService.GetAllFuelCostsAsync();

            if (fuelCosts == null)
            {
                _logger.LogWarning("No fuel costs found");
                return NotFound("No fuel costs found");
            }

            var fuelCostsDtos = _mapper.Map<IEnumerable<FuelCostsDto>>(fuelCosts);
            return Ok(fuelCostsDtos);
        }

        /// <summary>
        /// Gets fuel costs for a specific house
        /// </summary>
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FuelCostsDto>> GetFuelCostsByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting fuel costs for house ID: {HouseId}", houseId);

            var fuelCosts = await _fuelCostService.GetFuelCostsByHouseIdAsync(houseId);

            if (fuelCosts == null)
            {
                _logger.LogWarning("Fuel costs not found for house ID: {HouseId}", houseId);
                return NotFound($"Fuel costs not found for house ID: {houseId}");
            }

            var fuelCostsDto = _mapper.Map<FuelCostsDto>(fuelCosts);
            return Ok(fuelCostsDto);
        }

        /// <summary>
        /// Gets a specific fuel costs by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FuelCostsDto>> GetFuelCostsById(Guid id)
        {
            _logger.LogInformation("Getting fuel costs with ID: {Id}", id);

            var fuelCosts = await _fuelCostService.GetFuelCostsByIdAsync(id);

            if (fuelCosts == null)
            {
                _logger.LogWarning("Fuel costs not found with ID: {Id}", id);
                return NotFound($"Fuel costs not found with ID: {id}");
            }

            var fuelCostsDto = _mapper.Map<FuelCostsDto>(fuelCosts);
            return Ok(fuelCostsDto);
        }

        /// <summary>
        /// Creates new fuel costs
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<FuelCostsDto>> CreateFuelCosts([FromBody] FuelCostsDto fuelCostsDto)
        {
            if (fuelCostsDto == null)
            {
                _logger.LogWarning("Fuel costs data is null");
                return BadRequest("Fuel costs data is required");
            }

            _logger.LogInformation("Creating new fuel costs for house ID: {HouseId}", fuelCostsDto.HouseId);

            var fuelCosts = _mapper.Map<FuelCosts>(fuelCostsDto);

            var createdFuelCosts = await _fuelCostService.CreateFuelCostsAsync(fuelCosts);
            var createdFuelCostsDto = _mapper.Map<FuelCostsDto>(createdFuelCosts);

            return CreatedAtAction(
                nameof(GetFuelCostsById),
                new { id = createdFuelCostsDto.Id },
                createdFuelCostsDto);
        }

        /// <summary>
        /// Updates an existing fuel costs
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateFuelCosts(Guid id, [FromBody] FuelCostsDto fuelCostsDto)
        {
            if (fuelCostsDto == null)
            {
                _logger.LogWarning("Fuel costs data is null");
                return BadRequest("Fuel costs data is required");
            }

            if (id != fuelCostsDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, fuelCostsDto.Id);
                return BadRequest("ID mismatch");
            }

            _logger.LogInformation("Updating fuel costs with ID: {Id}", id);

            var fuelCosts = _mapper.Map<FuelCosts>(fuelCostsDto);

            try
            {
                await _fuelCostService.UpdateFuelCostsAsync(fuelCosts);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
            {
                _logger.LogWarning("Fuel costs not found with ID: {Id}", id);
                return NotFound($"Fuel costs not found with ID: {id}");
            }

            return NoContent();
        }

        /// <summary>
        /// Deletes fuel costs
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteFuelCosts(Guid id)
        {
            var existingFuelCosts = await _fuelCostService.GetFuelCostsByIdAsync(id);
            if (existingFuelCosts == null)
            {
                _logger.LogWarning("Fuel costs not found with ID: {Id}", id);
                return NotFound($"Fuel costs not found with ID: {id}");
            }

            _logger.LogInformation("Deleting fuel costs with ID: {Id}", id);
            await _fuelCostService.DeleteFuelCostsAsync(id);

            return NoContent();
        }

        /// <summary>
        /// Gets fuel costs for a specific energy upgrade
        /// </summary>
        [HttpGet("energy-upgrades/{energyUpgradeId}/fuel-costs")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FuelCostsDto>> GetFuelCostsByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting fuel costs for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var fuelCosts = await _fuelCostService.GetFuelCostsByEnergyUpgradeIdAsync(energyUpgradeId);

            if (fuelCosts == null)
            {
                _logger.LogWarning("Fuel costs not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Fuel costs not found for energy upgrade ID: {energyUpgradeId}");
            }

            var fuelCostsDto = _mapper.Map<FuelCostsDto>(fuelCosts);
            return Ok(fuelCostsDto);
        }

        /// <summary>
        /// Creates a copy of existing fuel costs for energy upgrade purposes
        /// Only requires energyUpgradeId - automatically copies all values from the base fuel costs
        /// </summary>
        [HttpPost("fuel-costs/{baseFuelCostsId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<FuelCostsDto>> DuplicateFuelCostsForEnergyUpgrade(
            Guid baseFuelCostsId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating fuel costs {BaseFuelCostsId} for energy upgrade {EnergyUpgradeId}",
                    baseFuelCostsId, energyUpgradeId);

                // Get the base fuel costs
                var baseFuelCosts = await _fuelCostService.GetFuelCostsByIdAsync(baseFuelCostsId);
                if (baseFuelCosts == null)
                {
                    _logger.LogWarning("Base fuel costs with ID: {BaseFuelCostsId} not found", baseFuelCostsId);
                    return NotFound($"Base fuel costs with ID {baseFuelCostsId} not found");
                }

                // Create a duplicate with new ID but same values
                var duplicatedFuelCosts = await _fuelCostService.DuplicateFuelCostsForEnergyUpgradeAsync(baseFuelCosts, energyUpgradeId);

                var duplicatedFuelCostsDto = _mapper.Map<FuelCostsDto>(duplicatedFuelCosts);

                _logger.LogInformation("Successfully duplicated fuel costs {BaseFuelCostsId} as {NewFuelCostsId} for energy upgrade {EnergyUpgradeId}",
                    baseFuelCostsId, duplicatedFuelCosts.Id, energyUpgradeId);

                return CreatedAtAction(nameof(GetFuelCostsByHouseId),
                    new { houseId = duplicatedFuelCosts.HouseId },
                    duplicatedFuelCostsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating fuel costs {BaseFuelCostsId} for energy upgrade {EnergyUpgradeId}",
                    baseFuelCostsId, energyUpgradeId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while duplicating the fuel costs for energy upgrade");
            }
        }
    }
}