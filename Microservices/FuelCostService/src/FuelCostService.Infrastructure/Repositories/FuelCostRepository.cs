using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Models;
using FuelCostService.Infrastructure.Data;

namespace FuelCostService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for FuelCosts operations
    /// </summary>
    public class FuelCostRepository : IFuelCostRepository
    {
        private readonly FuelCostDbContext _context;

        public FuelCostRepository(FuelCostDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<FuelCosts>> GetAllAsync()
        {
            return await _context.FuelCosts
                .Include(f => f.Electricity)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Electricity)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Electricity)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.NaturalGas)
                    .ThenInclude(e => e.Units)
                .Include(f => f.NaturalGas)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.NaturalGas)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Oil)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Oil)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Oil)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Propane)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Propane)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Propane)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Wood)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Wood)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Wood)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .ToListAsync();
        }

        public async Task<FuelCosts?> GetByHouseIdAsync(Guid houseId)
        {
            return await GetFuelCostsWithIncludes()
                .FirstOrDefaultAsync(f => f.HouseId == houseId && f.EnergyUpgradeId == null);
        }

        public async Task<FuelCosts?> GetByIdAsync(Guid id)
        {
            return await GetFuelCostsWithIncludes()
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<FuelCosts?> GetByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await GetFuelCostsWithIncludes()
                .FirstOrDefaultAsync(f => f.EnergyUpgradeId == energyUpgradeId);
        }

        public async Task<FuelCosts> CreateAsync(FuelCosts fuelCosts)
        {
            _context.FuelCosts.Add(fuelCosts);
            await _context.SaveChangesAsync();
            return fuelCosts;
        }

        public async Task UpdateAsync(FuelCosts fuelCosts)
        {
            _context.FuelCosts.Update(fuelCosts);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var fuelCosts = await _context.FuelCosts.FindAsync(id);
            if (fuelCosts != null)
            {
                _context.FuelCosts.Remove(fuelCosts);
                await _context.SaveChangesAsync();
            }
        }

        private IQueryable<FuelCosts> GetFuelCostsWithIncludes()
        {
            return _context.FuelCosts
                .Include(f => f.Electricity)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Electricity)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Electricity)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.NaturalGas)
                    .ThenInclude(e => e.Units)
                .Include(f => f.NaturalGas)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.NaturalGas)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Oil)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Oil)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Oil)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Propane)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Propane)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Propane)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Wood)
                    .ThenInclude(e => e.Units)
                .Include(f => f.Wood)
                    .ThenInclude(e => e.Minimum)
                .Include(f => f.Wood)
                    .ThenInclude(e => e.RateBlocks)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood);
        }
    }
}
