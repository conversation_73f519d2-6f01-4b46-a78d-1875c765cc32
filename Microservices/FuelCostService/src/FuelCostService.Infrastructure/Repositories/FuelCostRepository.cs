using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Models;
using FuelCostService.Infrastructure.Data;

namespace FuelCostService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for FuelCosts operations
    /// </summary>
    public class FuelCostRepository : IFuelCostRepository
    {
        private readonly FuelCostDbContext _context;

        public FuelCostRepository(FuelCostDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<FuelCosts>> GetAllAsync()
        {
            var fuelCostsList = await _context.FuelCosts
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .ToListAsync();

            // Load FuelCostByType collections for each FuelCosts
            foreach (var fuelCosts in fuelCostsList)
            {
                await LoadFuelCostByTypeCollections(fuelCosts);
            }

            return fuelCostsList;
        }

        public async Task<FuelCosts?> GetByHouseIdAsync(Guid houseId)
        {
            var fuelCosts = await _context.FuelCosts
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .FirstOrDefaultAsync(f => f.HouseId == houseId && f.EnergyUpgradeId == null);

            if (fuelCosts != null)
            {
                await LoadFuelCostByTypeCollections(fuelCosts);
            }

            return fuelCosts;
        }

        public async Task<FuelCosts?> GetByIdAsync(Guid id)
        {
            var fuelCosts = await _context.FuelCosts
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .FirstOrDefaultAsync(f => f.Id == id);

            if (fuelCosts != null)
            {
                await LoadFuelCostByTypeCollections(fuelCosts);
            }

            return fuelCosts;
        }

        public async Task<FuelCosts?> GetByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            var fuelCosts = await _context.FuelCosts
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .FirstOrDefaultAsync(f => f.EnergyUpgradeId == energyUpgradeId);

            if (fuelCosts != null)
            {
                await LoadFuelCostByTypeCollections(fuelCosts);
            }

            return fuelCosts;
        }

        public async Task<FuelCosts> CreateAsync(FuelCosts fuelCosts)
        {
            // Add the main FuelCosts entity
            _context.FuelCosts.Add(fuelCosts);
            await _context.SaveChangesAsync();

            // Now add all the FuelCostByType entities with the correct FuelCostsId
            await AddFuelCostByTypeEntities(fuelCosts);

            return fuelCosts;
        }

        public async Task UpdateAsync(FuelCosts fuelCosts)
        {
            // Update the main FuelCosts entity
            _context.FuelCosts.Update(fuelCosts);

            // Remove existing FuelCostByType entities
            var existingFuelCostByTypes = await _context.FuelCostByType
                .Where(f => f.FuelCostsId == fuelCosts.Id)
                .ToListAsync();
            _context.FuelCostByType.RemoveRange(existingFuelCostByTypes);

            await _context.SaveChangesAsync();

            // Add the updated FuelCostByType entities
            await AddFuelCostByTypeEntities(fuelCosts);
        }

        public async Task DeleteAsync(Guid id)
        {
            var fuelCosts = await _context.FuelCosts.FindAsync(id);
            if (fuelCosts != null)
            {
                _context.FuelCosts.Remove(fuelCosts);
                await _context.SaveChangesAsync();
            }
        }

        private async Task LoadFuelCostByTypeCollections(FuelCosts fuelCosts)
        {
            // Load all FuelCostByType entities for this FuelCosts
            var fuelCostByTypes = await _context.FuelCostByType
                .Include(f => f.Units)
                .Include(f => f.Minimum)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block1)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block2)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block3)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block4)
                .Where(f => f.FuelCostsId == fuelCosts.Id)
                .ToListAsync();

            // Clear existing collections
            fuelCosts.Electricity.Clear();
            fuelCosts.NaturalGas.Clear();
            fuelCosts.Oil.Clear();
            fuelCosts.Propane.Clear();
            fuelCosts.Wood.Clear();

            // Populate collections based on InternalId
            foreach (var fuelCostByType in fuelCostByTypes)
            {
                switch (fuelCostByType.InternalId)
                {
                    case 1:
                        fuelCosts.Electricity.Add(fuelCostByType);
                        break;
                    case 2:
                        fuelCosts.NaturalGas.Add(fuelCostByType);
                        break;
                    case 3:
                        fuelCosts.Oil.Add(fuelCostByType);
                        break;
                    case 4:
                        fuelCosts.Propane.Add(fuelCostByType);
                        break;
                    case 5:
                        fuelCosts.Wood.Add(fuelCostByType);
                        break;
                }
            }
        }

        private async Task AddFuelCostByTypeEntities(FuelCosts fuelCosts)
        {
            // Add all FuelCostByType entities from the collections
            var allFuelCostByTypes = new List<FuelCostByType>();

            // Add entities from each fuel type collection
            allFuelCostByTypes.AddRange(fuelCosts.Electricity);
            allFuelCostByTypes.AddRange(fuelCosts.NaturalGas);
            allFuelCostByTypes.AddRange(fuelCosts.Oil);
            allFuelCostByTypes.AddRange(fuelCosts.Propane);
            allFuelCostByTypes.AddRange(fuelCosts.Wood);

            // Process each FuelCostByType entity individually
            foreach (var fuelCostByType in allFuelCostByTypes)
            {
                // Set the FuelCostsId
                fuelCostByType.FuelCostsId = fuelCosts.Id;

                // Generate new IDs for all entities to avoid conflicts
                fuelCostByType.Id = Guid.NewGuid();

                // Handle Units (CodeAndText)
                if (fuelCostByType.Units != null)
                {
                    fuelCostByType.Units.Id = Guid.NewGuid();
                }

                // Handle Minimum
                if (fuelCostByType.Minimum != null)
                {
                    fuelCostByType.Minimum.Id = Guid.NewGuid();
                    fuelCostByType.Minimum.FuelCostByTypeId = fuelCostByType.Id;
                }

                // Handle RateBlocks and individual blocks
                if (fuelCostByType.RateBlocks != null)
                {
                    fuelCostByType.RateBlocks.Id = Guid.NewGuid();
                    fuelCostByType.RateBlocks.FuelCostByTypeId = fuelCostByType.Id;

                    if (fuelCostByType.RateBlocks.Block1 != null)
                    {
                        fuelCostByType.RateBlocks.Block1.Id = Guid.NewGuid();
                        fuelCostByType.RateBlocks.Block1.FuelCostByTypeId = fuelCostByType.Id;
                    }

                    if (fuelCostByType.RateBlocks.Block2 != null)
                    {
                        fuelCostByType.RateBlocks.Block2.Id = Guid.NewGuid();
                        fuelCostByType.RateBlocks.Block2.FuelCostByTypeId = fuelCostByType.Id;
                    }

                    if (fuelCostByType.RateBlocks.Block3 != null)
                    {
                        fuelCostByType.RateBlocks.Block3.Id = Guid.NewGuid();
                        fuelCostByType.RateBlocks.Block3.FuelCostByTypeId = fuelCostByType.Id;
                    }

                    if (fuelCostByType.RateBlocks.Block4 != null)
                    {
                        fuelCostByType.RateBlocks.Block4.Id = Guid.NewGuid();
                        fuelCostByType.RateBlocks.Block4.FuelCostByTypeId = fuelCostByType.Id;
                    }
                }

                // Add the complete entity graph to the context
                _context.FuelCostByType.Add(fuelCostByType);
            }

            // Save all entities in one transaction
            await _context.SaveChangesAsync();
        }


    }
}
