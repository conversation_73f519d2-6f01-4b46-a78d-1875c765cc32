using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Models;
using FuelCostService.Infrastructure.Data;

namespace FuelCostService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for FuelCosts operations
    /// </summary>
    public class FuelCostRepository : IFuelCostRepository
    {
        private readonly FuelCostDbContext _context;

        public FuelCostRepository(FuelCostDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<FuelCosts>> GetAllAsync()
        {
            return await _context.FuelCosts
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<FuelCosts?> GetByHouseIdAsync(Guid houseId)
        {
            return await GetFuelCostsWithIncludes()
                .AsNoTracking()
                .FirstOrDefaultAsync(f => f.HouseId == houseId);
        }

        public async Task<FuelCosts?> GetByIdAsync(Guid id)
        {
            return await GetFuelCostsWithIncludes()
                .AsNoTracking()
                .FirstOrDefaultAsync(f => f.Id == id);
        }



        public async Task<FuelCosts> CreateAsync(FuelCosts fuelCosts)
        {
            _context.FuelCosts.Add(fuelCosts);
            await _context.SaveChangesAsync();
            return fuelCosts;
        }

        public async Task UpdateAsync(FuelCosts fuelCosts)
        {
            // Clear any tracked entities to avoid conflicts
            _context.ChangeTracker.Clear();
            
            // Update the entity
            _context.FuelCosts.Update(fuelCosts);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var fuelCosts = await _context.FuelCosts.FindAsync(id);
            if (fuelCosts != null)
            {
                _context.FuelCosts.Remove(fuelCosts);
                await _context.SaveChangesAsync();
            }
        }

        private IQueryable<FuelCosts> GetFuelCostsWithIncludes()
        {
            return _context.FuelCosts
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Electricity)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.NaturalGas)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Oil)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Propane)
                .Include(f => f.Monthly)
                    .ThenInclude(m => m.Wood);
        }

        // Method for getting entities that need to be updated (with tracking)
        public async Task<FuelCosts?> GetByIdForUpdateAsync(Guid id)
        {
            return await GetFuelCostsWithIncludes()
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        // Additional methods to work with FuelCostByType entities separately
        // since they're ignored in the main FuelCosts entity

        public async Task<List<FuelCostByType>> GetFuelCostByTypesByFuelCostsIdAsync(Guid fuelCostsId)
        {
            return await _context.FuelCostByType
                .Where(f => f.FuelCostsId == fuelCostsId)
                .Include(f => f.Minimum)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block1)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block2)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block3)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block4)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<FuelCostByType?> GetFuelCostByTypeAsync(Guid fuelCostsId, int internalId)
        {
            return await _context.FuelCostByType
                .Where(f => f.FuelCostsId == fuelCostsId && f.InternalId == internalId)
                .Include(f => f.Minimum)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block1)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block2)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block3)
                .Include(f => f.RateBlocks)
                    .ThenInclude(r => r.Block4)
                .AsNoTracking()
                .FirstOrDefaultAsync();
        }

        public async Task<FuelCostByType> CreateFuelCostByTypeAsync(FuelCostByType fuelCostByType)
        {
            // Detach any existing tracked entity with the same ID to avoid conflicts
            var existingEntry = _context.Entry(fuelCostByType);
            if (existingEntry.State != Microsoft.EntityFrameworkCore.EntityState.Detached)
            {
                existingEntry.State = Microsoft.EntityFrameworkCore.EntityState.Detached;
            }

            // Check if there's already a tracked entity with the same ID
            var trackedEntity = _context.ChangeTracker.Entries<FuelCostByType>()
                .FirstOrDefault(e => e.Entity.Id == fuelCostByType.Id);

            if (trackedEntity is not null)
            {
                trackedEntity.State = Microsoft.EntityFrameworkCore.EntityState.Detached;
            }

            _context.FuelCostByType.Add(fuelCostByType);
            await _context.SaveChangesAsync();
            return fuelCostByType;
        }

        public async Task UpdateFuelCostByTypeAsync(FuelCostByType fuelCostByType)
        {
            _context.ChangeTracker.Clear();
            _context.FuelCostByType.Update(fuelCostByType);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteFuelCostByTypeAsync(Guid id)
        {
            var fuelCostByType = await _context.FuelCostByType.FindAsync(id);
            if (fuelCostByType != null)
            {
                _context.FuelCostByType.Remove(fuelCostByType);
                await _context.SaveChangesAsync();
            }
        }

        // Method to get complete FuelCosts with all related data including FuelCostByType
        public async Task<FuelCosts?> GetCompleteByIdAsync(Guid id)
        {
            var fuelCosts = await GetFuelCostsWithIncludes()
                .AsNoTracking()
                .FirstOrDefaultAsync(f => f.Id == id);

            return fuelCosts;
        }

        // Helper method to get all CodeAndText entities (for Units lookup)
        public async Task<List<CodeAndText>> GetAllCodeAndTextAsync()
        {
            return await _context.CodeAndText
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<CodeAndText?> GetCodeAndTextByCodeAsync(string code)
        {
            return await _context.CodeAndText
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Code == code);
        }
    }
}